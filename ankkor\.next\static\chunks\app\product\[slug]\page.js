/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/product/[slug]/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Ccomponents%5C%5Cproduct%5C%5CProductDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Ccomponents%5C%5Cproduct%5C%5CProductDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/product/ProductDetail.tsx */ \"(app-pages-browser)/./src/components/product/ProductDetail.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2Fua2tvcndvbyU1QyU1Q2Fua2tvciU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNwcm9kdWN0JTVDJTVDUHJvZHVjdERldGFpbC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQW9JIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ODJhZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJFOlxcXFxhbmtrb3J3b29cXFxcYW5ra29yXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHByb2R1Y3RcXFxcUHJvZHVjdERldGFpbC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Ccomponents%5C%5Cproduct%5C%5CProductDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wifi.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Wifi; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Wifi = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Wifi\", [\n    [\n        \"path\",\n        {\n            d: \"M5 13a10 10 0 0 1 14 0\",\n            key: \"6v8j51\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8.5 16.5a5 5 0 0 1 7 0\",\n            key: \"sej527\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 8.82a15 15 0 0 1 20 0\",\n            key: \"dnpr2z\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"20\",\n            y2: \"20\",\n            key: \"of4bc4\"\n        }\n    ]\n]);\n //# sourceMappingURL=wifi.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/product/ProductDetail.tsx":
/*!**************************************************!*\
  !*** ./src/components/product/ProductDetail.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _lib_localCartStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/localCartStore */ \"(app-pages-browser)/./src/lib/localCartStore.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_cart_CartProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/cart/CartProvider */ \"(app-pages-browser)/./src/components/cart/CartProvider.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus,ShoppingBag,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _hooks_useStockUpdates__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useStockUpdates */ \"(app-pages-browser)/./src/hooks/useStockUpdates.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst ProductDetail = (param)=>{\n    let { product } = param;\n    var _productImages_selectedImage;\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [selectedVariant, setSelectedVariant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isAddingToCart, setIsAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const cartStore = (0,_lib_localCartStore__WEBPACK_IMPORTED_MODULE_3__.useLocalCartStore)();\n    const { openCart } = (0,_components_cart_CartProvider__WEBPACK_IMPORTED_MODULE_5__.useCart)();\n    // Extract product data\n    const { id, databaseId, name, description, shortDescription, price, regularPrice, onSale, stockStatus, image, galleryImages, attributes, type, variations } = product;\n    // Real-time stock updates\n    const { stockData, isConnected } = (0,_hooks_useStockUpdates__WEBPACK_IMPORTED_MODULE_6__.useProductStockUpdates)((databaseId === null || databaseId === void 0 ? void 0 : databaseId.toString()) || \"\", true);\n    // Use real-time stock data if available, otherwise fall back to product data\n    const currentStockStatus = stockData.stockStatus || stockStatus;\n    const currentStockQuantity = stockData.stockQuantity;\n    // Determine if product is a variable product\n    const isVariableProduct = type === \"VARIABLE\";\n    // Format product images for display\n    const productImages = [\n        (image === null || image === void 0 ? void 0 : image.sourceUrl) ? {\n            sourceUrl: image.sourceUrl,\n            altText: image.altText || name\n        } : null,\n        ...(galleryImages === null || galleryImages === void 0 ? void 0 : galleryImages.nodes) || []\n    ].filter(Boolean);\n    // Handle quantity changes\n    const incrementQuantity = ()=>setQuantity((prev)=>prev + 1);\n    const decrementQuantity = ()=>setQuantity((prev)=>prev > 1 ? prev - 1 : 1);\n    // Handle attribute selection\n    const handleAttributeChange = (attributeName, value)=>{\n        setSelectedAttributes((prev)=>({\n                ...prev,\n                [attributeName]: value\n            }));\n        // Find matching variant if all attributes are selected\n        if (isVariableProduct && (variations === null || variations === void 0 ? void 0 : variations.nodes)) {\n            var _attributes_nodes;\n            const updatedAttributes = {\n                ...selectedAttributes,\n                [attributeName]: value\n            };\n            // Check if all required attributes are selected\n            const allAttributesSelected = attributes === null || attributes === void 0 ? void 0 : (_attributes_nodes = attributes.nodes) === null || _attributes_nodes === void 0 ? void 0 : _attributes_nodes.every((attr)=>updatedAttributes[attr.name]);\n            if (allAttributesSelected) {\n                // Find matching variant\n                const matchingVariant = variations.nodes.find((variant)=>{\n                    return variant.attributes.nodes.every((attr)=>{\n                        const selectedValue = updatedAttributes[attr.name];\n                        return attr.value === selectedValue;\n                    });\n                });\n                if (matchingVariant) {\n                    setSelectedVariant(matchingVariant);\n                } else {\n                    setSelectedVariant(null);\n                }\n            }\n        }\n    };\n    // Handle add to cart\n    const handleAddToCart = async ()=>{\n        setIsAddingToCart(true);\n        try {\n            var _productImages_, _productImages_1;\n            const productToAdd = {\n                productId: databaseId.toString(),\n                quantity,\n                name,\n                price: (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price,\n                image: {\n                    url: ((_productImages_ = productImages[0]) === null || _productImages_ === void 0 ? void 0 : _productImages_.sourceUrl) || \"\",\n                    altText: ((_productImages_1 = productImages[0]) === null || _productImages_1 === void 0 ? void 0 : _productImages_1.altText) || name\n                }\n            };\n            await cartStore.addToCart(productToAdd);\n            openCart();\n        } catch (error) {\n            console.error(\"Error adding product to cart:\", error);\n        } finally{\n            setIsAddingToCart(false);\n        }\n    };\n    // Check if product is out of stock (use real-time data if available)\n    const isOutOfStock = (currentStockStatus || stockStatus) !== \"IN_STOCK\" && (currentStockStatus || stockStatus) !== \"instock\";\n    // Check if product can be added to cart (has all required attributes selected for variable products)\n    const canAddToCart = !isVariableProduct || isVariableProduct && selectedVariant;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative aspect-square bg-[#f4f3f0] overflow-hidden\",\n                            children: ((_productImages_selectedImage = productImages[selectedImage]) === null || _productImages_selectedImage === void 0 ? void 0 : _productImages_selectedImage.sourceUrl) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: productImages[selectedImage].sourceUrl,\n                                alt: productImages[selectedImage].altText || name,\n                                fill: true,\n                                sizes: \"(max-width: 768px) 100vw, 50vw\",\n                                priority: true,\n                                className: \"object-cover\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, undefined),\n                        productImages.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-5 gap-2\",\n                            children: productImages.map((img, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedImage(index),\n                                    className: \"relative aspect-square bg-[#f4f3f0] \".concat(selectedImage === index ? \"ring-2 ring-[#2c2c27]\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: img.sourceUrl,\n                                        alt: img.altText || \"\".concat(name, \" - Image \").concat(index + 1),\n                                        fill: true,\n                                        sizes: \"(max-width: 768px) 20vw, 10vw\",\n                                        className: \"object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-serif text-[#2c2c27]\",\n                            children: name\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-medium text-[#2c2c27]\",\n                                    children: ((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price).toString().includes(\"₹\") || ((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price).toString().includes(\"$\") || ((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price).toString().includes(\"€\") || ((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price).toString().includes(\"\\xa3\") ? (selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price : \"₹\".concat((selectedVariant === null || selectedVariant === void 0 ? void 0 : selectedVariant.price) || price)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined),\n                                onSale && regularPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm line-through text-[#8a8778]\",\n                                    children: regularPrice.toString().includes(\"₹\") || regularPrice.toString().includes(\"$\") || regularPrice.toString().includes(\"€\") || regularPrice.toString().includes(\"\\xa3\") ? regularPrice : \"₹\".concat(regularPrice)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, undefined),\n                        shortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm text-[#5c5c52]\",\n                            dangerouslySetInnerHTML: {\n                                __html: shortDescription\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, undefined),\n                        isVariableProduct && (attributes === null || attributes === void 0 ? void 0 : attributes.nodes) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: attributes.nodes.map((attribute)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-[#2c2c27]\",\n                                            children: attribute.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: attribute.options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleAttributeChange(attribute.name, option),\n                                                    className: \"px-4 py-2 border \".concat(selectedAttributes[attribute.name] === option ? \"border-[#2c2c27] bg-[#2c2c27] text-white\" : \"border-gray-300 hover:border-[#8a8778]\"),\n                                                    children: option\n                                                }, option, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, attribute.name, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-[#5c5c52]\",\n                                    children: \"Quantity:\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center border border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: decrementQuantity,\n                                            disabled: quantity <= 1,\n                                            className: \"px-3 py-2 hover:bg-gray-100\",\n                                            \"aria-label\": \"Decrease quantity\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-4 py-2 border-x border-gray-300\",\n                                            children: quantity\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: incrementQuantity,\n                                            className: \"px-3 py-2 hover:bg-gray-100\",\n                                            \"aria-label\": \"Increase quantity\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Availability: \"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: isOutOfStock ? \"text-red-600\" : \"text-green-600\",\n                                            children: isOutOfStock ? \"Out of Stock\" : \"In Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 text-xs text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Live\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !isConnected && stockData.lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 text-xs text-gray-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Offline\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined),\n                                currentStockQuantity !== undefined && currentStockQuantity !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 mt-1\",\n                                    children: currentStockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            currentStockQuantity,\n                                            \" items available\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 19\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-600\",\n                                        children: \"No items in stock\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined),\n                                stockData.lastUpdated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        \"Last updated: \",\n                                        new Date(stockData.lastUpdated).toLocaleTimeString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                delay: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleAddToCart,\n                                    disabled: isOutOfStock || isAddingToCart || !canAddToCart,\n                                    className: \"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_ShoppingBag_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isAddingToCart ? \"Adding...\" : \"Add to Cart\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, undefined),\n                                isVariableProduct && !canAddToCart && !isOutOfStock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-sm text-red-600\",\n                                    children: \"Please select all options to add this product to your cart\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined),\n                        description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-12 border-t border-gray-200 pt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-serif mb-4 text-[#2c2c27]\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose prose-sm text-[#5c5c52]\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: description\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\product\\\\ProductDetail.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductDetail, \"5fcxGKeKMcbfixugxgSV7PpNwic=\", false, function() {\n    return [\n        _lib_localCartStore__WEBPACK_IMPORTED_MODULE_3__.useLocalCartStore,\n        _components_cart_CartProvider__WEBPACK_IMPORTED_MODULE_5__.useCart,\n        _hooks_useStockUpdates__WEBPACK_IMPORTED_MODULE_6__.useProductStockUpdates\n    ];\n});\n_c = ProductDetail;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductDetail);\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/product/ProductDetail.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useStockUpdates.ts":
/*!**************************************!*\
  !*** ./src/hooks/useStockUpdates.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useProductStockUpdates: function() { return /* binding */ useProductStockUpdates; },\n/* harmony export */   useStockUpdates: function() { return /* binding */ useStockUpdates; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useStockUpdates,useProductStockUpdates auto */ \nfunction useStockUpdates() {\n    let { productIds = [], onStockUpdate, enabled = true } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Memoize productIds to prevent unnecessary re-renders\n    const memoizedProductIds = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>productIds, [\n        productIds.join(\",\")\n    ]);\n    // Stable callback reference\n    const stableOnStockUpdate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((update)=>{\n        onStockUpdate === null || onStockUpdate === void 0 ? void 0 : onStockUpdate(update);\n    }, [\n        onStockUpdate\n    ]);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        // Clear any existing connection\n        if (eventSourceRef.current) {\n            eventSourceRef.current.close();\n            eventSourceRef.current = null;\n        }\n        // Clear any pending reconnection\n        if (reconnectTimeoutRef.current) {\n            clearTimeout(reconnectTimeoutRef.current);\n            reconnectTimeoutRef.current = null;\n        }\n        if (!enabled || memoizedProductIds.length === 0) {\n            console.log(\"Stock updates disabled or no products specified\");\n            return null;\n        }\n        const params = new URLSearchParams({\n            products: memoizedProductIds.join(\",\")\n        });\n        console.log(\"Creating new stock updates connection for products:\", memoizedProductIds);\n        const eventSource = new EventSource(\"/api/stock-updates?\".concat(params));\n        eventSourceRef.current = eventSource;\n        eventSource.onopen = ()=>{\n            console.log(\"Stock updates stream connected for products:\", memoizedProductIds);\n            setIsConnected(true);\n            setError(null);\n        };\n        eventSource.onmessage = (event)=>{\n            try {\n                const update = JSON.parse(event.data);\n                setLastUpdate(update);\n                if (update.type === \"stock_update\") {\n                    console.log(\"Stock update received:\", update);\n                    stableOnStockUpdate(update);\n                }\n            } catch (parseError) {\n                console.error(\"Error parsing stock update:\", parseError);\n            }\n        };\n        eventSource.onerror = (event)=>{\n            console.error(\"Stock updates stream error:\", event);\n            setIsConnected(false);\n            setError(\"Connection to stock updates failed\");\n            // Only attempt to reconnect if this is still the current connection\n            if (eventSourceRef.current === eventSource) {\n                reconnectTimeoutRef.current = setTimeout(()=>{\n                    if (eventSourceRef.current === eventSource && eventSource.readyState === EventSource.CLOSED) {\n                        console.log(\"Attempting to reconnect stock updates...\");\n                        connect();\n                    }\n                }, 5000);\n            }\n        };\n        return eventSource;\n    }, [\n        memoizedProductIds,\n        stableOnStockUpdate,\n        enabled\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        connect();\n        return ()=>{\n            // Cleanup function\n            if (eventSourceRef.current) {\n                console.log(\"Cleaning up stock updates connection\");\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n                setIsConnected(false);\n            }\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n        };\n    }, [\n        connect\n    ]);\n    return {\n        isConnected,\n        lastUpdate,\n        error,\n        reconnect: connect\n    };\n}\n// Hook for single product stock updates\nfunction useProductStockUpdates(productId) {\n    let enabled = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    const [stockData, setStockData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Memoize the productIds array to prevent recreation\n    const memoizedProductIds = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>productId ? [\n            productId\n        ] : [], [\n        productId\n    ]);\n    // Stable callback that doesn't change unless productId changes\n    const handleStockUpdate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((update)=>{\n        if (update.productId === productId) {\n            setStockData((prevData)=>({\n                    ...prevData,\n                    stockStatus: update.stockStatus,\n                    stockQuantity: update.stockQuantity,\n                    availableForSale: update.availableForSale,\n                    lastUpdated: update.timestamp\n                }));\n        }\n    }, [\n        productId\n    ]);\n    const { isConnected, error } = useStockUpdates({\n        productIds: memoizedProductIds,\n        onStockUpdate: handleStockUpdate,\n        enabled: enabled && !!productId\n    });\n    return {\n        stockData,\n        isConnected,\n        error\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useStockUpdates.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["framework-node_modules_next_dist_a","framework-node_modules_next_dist_client_a","framework-node_modules_next_dist_client_components_ap","framework-node_modules_next_dist_client_components_b","framework-node_modules_next_dist_client_components_layout-router_js-4906aef6","framework-node_modules_next_dist_client_components_m","framework-node_modules_next_dist_client_components_p","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Te","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_R","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B","framework-node_modules_next_dist_client_components_rea","framework-node_modules_next_dist_client_components_re","framework-node_modules_next_dist_client_components_router-reducer_co","framework-node_modules_next_dist_client_components_router-reducer_fe","framework-node_modules_next_dist_client_components_router-reducer_h","framework-node_modules_next_dist_client_components_router-reducer_pp","framework-node_modules_next_dist_client_components_router-reducer_reducers_f","framework-node_modules_next_dist_client_components_router-reducer_reducers_r","framework-node_modules_next_dist_client_components_router-reducer_r","framework-node_modules_next_dist_client_c","framework-node_modules_next_dist_client_g","framework-node_modules_next_dist_client_l","framework-node_modules_next_dist_compiled_a","framework-node_modules_next_dist_compiled_m","framework-node_modules_next_dist_compiled_react-dom_cjs_react-dom_development_js-3041f41d","framework-node_modules_next_dist_compiled_react-d","framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da","framework-node_modules_next_dist_compiled_react_cjs_react-jsx-dev-runtime_development_js-12999a20","framework-node_modules_next_dist_compiled_react_c","framework-node_modules_next_dist_compiled_react_cjs_react_development_js-a784779d","framework-node_modules_next_dist_compiled_r","framework-node_modules_next_dist_l","framework-node_modules_next_dist_shared_lib_a","framework-node_modules_next_dist_shared_lib_ha","framework-node_modules_next_dist_shared_lib_h","framework-node_modules_next_dist_shared_lib_lazy-dynamic_b","framework-node_modules_next_dist_shared_lib_m","framework-node_modules_next_dist_shared_lib_router-","framework-node_modules_next_dist_shared_lib_router_utils_o","framework-node_modules_next_dist_shared_lib_r","framework-node_modules_next_d","framework-node_modules_next_font_google_target_css-0","commons-_","commons-node_modules_framer-motion_dist_es_animation_animators_i","commons-node_modules_framer-motion_dist_es_a","commons-node_modules_framer-motion_dist_es_d","commons-node_modules_framer-motion_dist_es_motion_f","commons-node_modules_framer-motion_dist_es_projection_a","commons-node_modules_framer-motion_dist_es_projection_node_create-projection-node_mjs-d9cf742e","commons-node_modules_framer-motion_dist_es_render_VisualElement_mjs-19d9658a","commons-node_modules_framer-motion_dist_es_render_d","commons-node_modules_framer-motion_dist_es_r","commons-node_modules_framer-motion_dist_es_value_i","commons-node_modules_go","commons-node_modules_graphql_language_a","commons-node_modules_graphql_language_parser_mjs-c45803c0","commons-node_modules_graphql_language_p","commons-node_modules_l","commons-node_modules_react-hook-form_dist_index_esm_mjs-74baa987","commons-node_modules_tailwind-merge_dist_bundle-mjs_mjs-a19ea93e","commons-node_modules_upstash_redis_chunk-5XANP4AV_mjs-ec81489a","commons-node_modules_zustand_esm_i","commons-src_components_auth_AuthForm_tsx-6dd93bd8","commons-src_components_c","commons-src_com","commons-src_lib_c","commons-src_lib_s","commons-src_lib_woocommerce_ts-ea0e4c9f","main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Ccomponents%5C%5Cproduct%5C%5CProductDetail.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);